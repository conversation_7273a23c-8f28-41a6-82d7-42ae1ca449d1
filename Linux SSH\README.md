# Node.js 一键安装脚本

一个功能完整的Node.js自动安装脚本，支持多种Linux发行版，提供版本选择、目录自定义、环境变量自动配置等功能。

## 功能特性

✅ **多发行版支持**: 支持主流Linux发行版及其衍生版本  
✅ **智能检测**: 自动检测发行版家族和包管理器  
✅ **版本选择**: 最新LTS、最新稳定版、历史版本浏览  
✅ **分页浏览**: 版本列表分页显示，每页10个版本  
✅ **API获取**: 自动从官方API获取最新版本信息  
✅ **目录选择**: 支持默认目录、用户目录、自定义目录  
✅ **环境配置**: 自动配置PATH环境变量并立即生效  
✅ **依赖检测**: 自动安装所需依赖包  
✅ **现有检测**: 检测并备份现有Node.js安装  
✅ **错误处理**: 完善的错误处理和清理机制  

## 系统要求

- Linux操作系统 (x64, ARM64, ARMv7)
- Bash shell
- 网络连接
- 基本系统工具 (自动安装缺失的依赖)

## 使用方法

### 1. 下载脚本

```bash
# 使用curl下载
curl -O https://raw.githubusercontent.com/your-repo/install_nodejs.sh

# 或使用wget下载
wget https://raw.githubusercontent.com/your-repo/install_nodejs.sh
```

### 2. 添加执行权限

```bash
chmod +x install_nodejs.sh
```

### 3. 运行脚本

```bash
# 普通用户运行 (推荐)
./install_nodejs.sh

# 或以root用户运行
sudo ./install_nodejs.sh
```

## 权限说明

- **普通用户**: 脚本会自动使用sudo获取必要权限来安装依赖和创建系统目录
- **root用户**: 直接执行，无需额外权限处理
- **推荐方式**: 使用普通用户运行脚本，让脚本自动处理权限问题

## 使用流程

1. **欢迎界面**: 显示功能特性和系统信息
2. **现有检测**: 检查是否已安装Node.js，提供备份选项
3. **依赖安装**: 根据发行版自动安装必要依赖
4. **版本选择**: 
   - 最新LTS版本 (推荐)
   - 最新稳定版本
   - 浏览所有版本 (分页显示)
   - 手动输入版本号
5. **目录选择**:
   - 默认目录: `/usr/local`
   - 用户目录: `~/.local`
   - 自定义目录
6. **确认安装**: 显示安装信息确认
7. **下载安装**: 自动下载、解压、安装
8. **环境配置**: 配置PATH并立即生效
9. **验证完成**: 验证安装并显示版本信息

## 版本浏览功能

脚本提供了强大的版本浏览功能：

- **分页显示**: 每页显示10个版本
- **导航控制**: 
  - `n` - 下一页
  - `p` - 上一页  
  - `s` - 选择版本
  - `q` - 返回主菜单
- **版本验证**: 自动验证输入的版本号是否存在

## 支持的发行版

### Debian 家族
| 发行版 | 包管理器 | 依赖包 | 主要用途 |
|--------|----------|--------|----------|
| **Debian** | APT (dpkg) | curl, wget, xz-utils | 服务器, 桌面, 稳定 |
| **Ubuntu** | APT (dpkg) | curl, wget, xz-utils | 桌面, 服务器, 云 |
| **Linux Mint** | APT (dpkg) | curl, wget, xz-utils | 桌面, 易于使用 |
| **Pop!_OS** | APT (dpkg) | curl, wget, xz-utils | 桌面, STEM 专业 |
| **Zorin OS** | APT (dpkg) | curl, wget, xz-utils | 桌面, Windows 风格 |
| **Elementary OS** | APT (dpkg) | curl, wget, xz-utils | 桌面, 美观简洁 |
| **Kali Linux** | APT (dpkg) | curl, wget, xz-utils | 安全审计, 渗透测试 |

### Red Hat 家族
| 发行版 | 包管理器 | 依赖包 | 主要用途 |
|--------|----------|--------|----------|
| **RHEL** | DNF/YUM (RPM) | curl, wget, xz | 企业级, 商业支持 |
| **Fedora** | DNF/YUM (RPM) | curl, wget, xz | 桌面, 开发者, 创新 |
| **CentOS Stream** | DNF/YUM (RPM) | curl, wget, xz | RHEL 上游开发平台 |
| **Rocky Linux** | DNF/YUM (RPM) | curl, wget, xz | 企业级, RHEL 替代 |
| **AlmaLinux** | DNF/YUM (RPM) | curl, wget, xz | 企业级, RHEL 替代 |

### Arch 家族
| 发行版 | 包管理器 | 依赖包 | 主要用途 |
|--------|----------|--------|----------|
| **Arch Linux** | Pacman | curl, wget, xz | 滚动发布, 高度可定制 |
| **Manjaro** | Pacman | curl, wget, xz | 易于使用的 Arch |

### SUSE 家族
| 发行版 | 包管理器 | 依赖包 | 主要用途 |
|--------|----------|--------|----------|
| **openSUSE** | Zypper (RPM) | curl, wget, xz | 桌面, 服务器, 稳定 |
| **SLES** | Zypper (RPM) | curl, wget, xz | 企业级服务器 |

### 其他发行版
| 发行版 | 包管理器 | 依赖包 | 主要用途 |
|--------|----------|--------|----------|
| **Alpine** | APK | curl, wget, xz | 轻量级, 容器 |
| **Gentoo** | Portage | curl, wget, xz | 基于源码, 高度定制 |
| **Slackware** | pkgtools | 手动安装 | 类 UNIX, 历史悠久 |

## 安装目录说明

### 默认目录 (`/usr/local`)
- 系统级安装，所有用户可用
- 需要sudo权限
- 推荐用于服务器环境

### 用户目录 (`~/.local`)
- 用户级安装，仅当前用户可用
- 无需sudo权限
- 推荐用于开发环境

### 自定义目录
- 完全自定义安装位置
- 根据目录权限决定是否需要sudo

## 环境变量配置

脚本会自动配置以下环境变量：

```bash
export PATH="/path/to/nodejs/bin:$PATH"
export NODE_PATH="/path/to/nodejs/lib/node_modules"
```

配置文件包括：
- `~/.bashrc`
- `~/.zshrc` 
- `~/.profile`

## 测试脚本

项目包含一个测试脚本 `test_distro_detection.sh`，用于验证发行版检测功能：

```bash
chmod +x test_distro_detection.sh
./test_distro_detection.sh
```

测试脚本会显示：
- 系统识别文件
- 发行版检测结果
- 可用的包管理器
- 依赖工具状态

## 故障排除

### 1. 命令不可用
```bash
# 重新加载shell配置
source ~/.bashrc
# 或
source ~/.zshrc
# 或重启终端
```

### 2. 权限问题
```bash
# 检查安装目录权限
ls -la /usr/local/
# 或使用用户目录安装
```

### 3. 网络问题
```bash
# 检查网络连接
curl -I https://nodejs.org
# 或使用代理
export https_proxy=http://proxy:port
```

### 4. 依赖问题
```bash
# 手动安装依赖 (Ubuntu/Debian)
sudo apt-get install curl wget xz-utils
```

## 卸载方法

```bash
# 删除安装目录
sudo rm -rf /usr/local/node-v*
# 或
rm -rf ~/.local/node-v*

# 删除符号链接
sudo rm -f /usr/local/bin/{node,npm,npx}
# 或
rm -f ~/.local/bin/{node,npm,npx}

# 清理环境变量配置
# 编辑 ~/.bashrc, ~/.zshrc, ~/.profile
# 删除包含 "Node.js PATH" 的行
```

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个脚本！

## 更新日志

- v1.0.0: 初始版本，支持基本安装功能
- v1.1.0: 添加多发行版支持和依赖自动安装
- v1.2.0: 添加版本分页浏览和现有安装检测
- v1.3.0: 改进环境变量配置和错误处理
- v1.4.0: 扩展发行版支持，添加智能家族检测
