#!/bin/bash

# 测试Node.js下载的简化脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 获取系统架构
get_arch() {
    case $(uname -m) in
        x86_64) echo "x64" ;;
        aarch64|arm64) echo "arm64" ;;
        armv7l) echo "armv7l" ;;
        *) echo "unknown" ;;
    esac
}

print_message $GREEN "=== Node.js 下载测试 ==="
echo

# 系统信息
ARCH=$(get_arch)
print_message $BLUE "系统架构: $ARCH ($(uname -m))"
echo

# 测试版本列表
TEST_VERSIONS=("v20.16.0" "v20.11.0" "v18.19.0" "v22.11.0")

for VERSION in "${TEST_VERSIONS[@]}"; do
    print_message $BLUE "测试版本: $VERSION"
    
    FILENAME="node-${VERSION}-linux-${ARCH}.tar.xz"
    DOWNLOAD_URL="https://nodejs.org/dist/${VERSION}/${FILENAME}"
    
    echo "文件名: $FILENAME"
    echo "下载URL: $DOWNLOAD_URL"
    
    # 测试URL可访问性
    print_message $YELLOW "检查URL可访问性..."
    if command -v curl >/dev/null 2>&1; then
        HTTP_CODE=$(curl -I -s -o /dev/null -w "%{http_code}" "$DOWNLOAD_URL" 2>/dev/null || echo "000")
        if [[ "$HTTP_CODE" == "200" ]]; then
            print_message $GREEN "✓ URL可访问 (HTTP $HTTP_CODE)"
            
            # 获取文件大小
            CONTENT_LENGTH=$(curl -I -s "$DOWNLOAD_URL" 2>/dev/null | grep -i content-length | cut -d' ' -f2 | tr -d '\r')
            if [[ -n "$CONTENT_LENGTH" ]]; then
                SIZE_MB=$((CONTENT_LENGTH / 1024 / 1024))
                echo "文件大小: ${SIZE_MB}MB"
            fi
            
            # 尝试实际下载（只下载前1KB测试）
            print_message $YELLOW "测试下载前1KB..."
            if curl -r 0-1023 -s -o /dev/null "$DOWNLOAD_URL" 2>/dev/null; then
                print_message $GREEN "✓ 下载测试成功"
                
                # 询问是否完整下载
                echo
                read -p "是否要完整下载 $VERSION? [y/N]: " download_full
                if [[ $download_full =~ ^[Yy]$ ]]; then
                    print_message $BLUE "开始下载 $FILENAME..."
                    cd /tmp
                    if curl -L --progress-bar -o "$FILENAME" "$DOWNLOAD_URL"; then
                        print_message $GREEN "✓ 下载完成: /tmp/$FILENAME"
                        
                        # 验证文件
                        if [[ -f "$FILENAME" ]] && [[ -s "$FILENAME" ]]; then
                            ACTUAL_SIZE=$(stat -c%s "$FILENAME" 2>/dev/null || stat -f%z "$FILENAME" 2>/dev/null)
                            ACTUAL_SIZE_MB=$((ACTUAL_SIZE / 1024 / 1024))
                            print_message $GREEN "文件大小: ${ACTUAL_SIZE_MB}MB"
                            
                            # 测试解压
                            print_message $YELLOW "测试解压..."
                            if tar -tf "$FILENAME" >/dev/null 2>&1; then
                                print_message $GREEN "✓ 文件完整，可以解压"
                                
                                # 询问是否要安装
                                read -p "是否要安装到 /Files/Node? [y/N]: " install_now
                                if [[ $install_now =~ ^[Yy]$ ]]; then
                                    print_message $BLUE "开始安装..."
                                    
                                    # 创建安装目录
                                    mkdir -p /Files/Node
                                    
                                    # 解压
                                    tar -xf "$FILENAME"
                                    EXTRACTED_DIR=$(tar -tf "$FILENAME" | head -1 | cut -f1 -d"/")
                                    
                                    # 移动到安装目录
                                    mv "$EXTRACTED_DIR" "/Files/Node/node-$VERSION"
                                    
                                    # 创建符号链接
                                    mkdir -p /Files/Node/bin
                                    ln -sf "/Files/Node/node-$VERSION/bin/node" "/Files/Node/bin/node"
                                    ln -sf "/Files/Node/node-$VERSION/bin/npm" "/Files/Node/bin/npm"
                                    ln -sf "/Files/Node/node-$VERSION/bin/npx" "/Files/Node/bin/npx"
                                    
                                    # 配置环境变量
                                    echo 'export PATH="/Files/Node/bin:$PATH"' >> ~/.bashrc
                                    
                                    print_message $GREEN "✓ 安装完成！"
                                    print_message $YELLOW "请运行以下命令使环境变量生效:"
                                    echo "source ~/.bashrc"
                                    echo "或重新登录终端"
                                    echo
                                    print_message $BLUE "验证安装:"
                                    echo "/Files/Node/bin/node --version"
                                    echo "/Files/Node/bin/npm --version"
                                    
                                    # 清理下载文件
                                    rm -f "$FILENAME"
                                    
                                    exit 0
                                fi
                            else
                                print_message $RED "✗ 文件损坏，无法解压"
                            fi
                        else
                            print_message $RED "✗ 下载的文件无效"
                        fi
                    else
                        print_message $RED "✗ 下载失败"
                    fi
                fi
            else
                print_message $RED "✗ 下载测试失败"
            fi
        else
            print_message $RED "✗ URL不可访问 (HTTP $HTTP_CODE)"
        fi
    elif command -v wget >/dev/null 2>&1; then
        if wget --spider -q "$DOWNLOAD_URL" 2>/dev/null; then
            print_message $GREEN "✓ URL可访问"
        else
            print_message $RED "✗ URL不可访问"
        fi
    else
        print_message $RED "✗ 需要curl或wget"
    fi
    
    echo
    echo "----------------------------------------"
    echo
done

print_message $BLUE "测试完成！"
print_message $YELLOW "如果所有版本都失败，请检查:"
echo "1. 网络连接: ping nodejs.org"
echo "2. DNS解析: nslookup nodejs.org"
echo "3. 防火墙设置"
echo "4. 代理配置"
