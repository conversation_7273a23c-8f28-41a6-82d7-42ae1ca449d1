#!/bin/bash

# 测试发行版检测功能
# 这个脚本用于测试 install_nodejs.sh 中的发行版检测功能

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 从主脚本中复制的函数
detect_distro() {
    local distro_id=""
    local distro_name=""
    
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        distro_id="$ID"
        distro_name="$NAME"
    elif [[ -f /etc/redhat-release ]]; then
        distro_id="rhel"
        distro_name="Red Hat Enterprise Linux"
    elif [[ -f /etc/debian_version ]]; then
        distro_id="debian"
        distro_name="Debian"
    elif [[ -f /etc/alpine-release ]]; then
        distro_id="alpine"
        distro_name="Alpine Linux"
    elif [[ -f /etc/gentoo-release ]]; then
        distro_id="gentoo"
        distro_name="Gentoo"
    elif [[ -f /etc/slackware-version ]]; then
        distro_id="slackware"
        distro_name="Slackware"
    else
        distro_id="unknown"
        distro_name="Unknown Linux"
    fi
    
    echo "$distro_id"
}

get_distro_info() {
    local distro_id=$(detect_distro)
    local distro_name="Unknown"
    local distro_family="Unknown"
    
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        distro_name="$NAME"
        
        case "$ID" in
            ubuntu|debian|mint|pop|zorin|elementary|kali)
                distro_family="Debian"
                ;;
            rhel|centos|fedora|rocky|almalinux)
                distro_family="Red Hat"
                ;;
            arch|manjaro)
                distro_family="Arch"
                ;;
            opensuse*|sles)
                distro_family="SUSE"
                ;;
            gentoo)
                distro_family="Gentoo"
                ;;
            alpine)
                distro_family="Alpine"
                ;;
            slackware)
                distro_family="Slackware"
                ;;
            *)
                if [[ -n "$ID_LIKE" ]]; then
                    case "$ID_LIKE" in
                        *debian*)
                            distro_family="Debian"
                            ;;
                        *rhel*|*fedora*)
                            distro_family="Red Hat"
                            ;;
                        *arch*)
                            distro_family="Arch"
                            ;;
                        *suse*)
                            distro_family="SUSE"
                            ;;
                    esac
                fi
                ;;
        esac
    fi
    
    echo "$distro_id|$distro_name|$distro_family"
}

# 主测试函数
main() {
    print_message $GREEN "=================================="
    print_message $GREEN "   发行版检测测试"
    print_message $GREEN "=================================="
    echo
    
    # 显示系统文件信息
    print_message $BLUE "系统识别文件:"
    for file in /etc/os-release /etc/redhat-release /etc/debian_version /etc/alpine-release /etc/gentoo-release /etc/slackware-version; do
        if [[ -f "$file" ]]; then
            print_message $GREEN "✓ $file 存在"
        else
            print_message $YELLOW "✗ $file 不存在"
        fi
    done
    echo
    
    # 显示 /etc/os-release 内容
    if [[ -f /etc/os-release ]]; then
        print_message $BLUE "/etc/os-release 内容:"
        cat /etc/os-release | while read line; do
            echo "  $line"
        done
        echo
    fi
    
    # 测试检测函数
    local distro_id=$(detect_distro)
    local distro_info=$(get_distro_info)
    local distro_name=$(echo "$distro_info" | cut -d'|' -f2)
    local distro_family=$(echo "$distro_info" | cut -d'|' -f3)
    
    print_message $BLUE "检测结果:"
    print_message $GREEN "- 发行版 ID: $distro_id"
    print_message $GREEN "- 发行版名称: $distro_name"
    print_message $GREEN "- 发行版家族: $distro_family"
    echo
    
    # 显示包管理器信息
    print_message $BLUE "可用的包管理器:"
    local package_managers=("apt" "apt-get" "dnf" "yum" "zypper" "pacman" "apk" "emerge" "installpkg")
    for pm in "${package_managers[@]}"; do
        if command -v "$pm" >/dev/null 2>&1; then
            local pm_path=$(which "$pm")
            print_message $GREEN "✓ $pm ($pm_path)"
        else
            print_message $YELLOW "✗ $pm"
        fi
    done
    echo
    
    # 显示已安装的依赖
    print_message $BLUE "依赖工具检查:"
    local tools=("curl" "wget" "xz" "unxz" "tar")
    for tool in "${tools[@]}"; do
        if command -v "$tool" >/dev/null 2>&1; then
            local tool_path=$(which "$tool")
            local tool_version=$($tool --version 2>/dev/null | head -1 || echo "版本未知")
            print_message $GREEN "✓ $tool ($tool_path) - $tool_version"
        else
            print_message $RED "✗ $tool 未安装"
        fi
    done
    echo
    
    print_message $GREEN "测试完成！"
}

# 运行测试
main "$@"
