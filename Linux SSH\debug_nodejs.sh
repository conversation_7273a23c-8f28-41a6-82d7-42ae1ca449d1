#!/bin/bash

# Node.js 安装调试脚本
# 用于调试安装过程中的问题

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 配置
NODE_API_URL="https://nodejs.org/dist/index.json"
NODE_DOWNLOAD_BASE="https://nodejs.org/dist"

# 获取系统信息
get_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        echo "linux"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        echo "darwin"
    else
        print_message $RED "不支持的操作系统: $OSTYPE"
        exit 1
    fi
}

get_arch() {
    local arch=$(uname -m)
    case $arch in
        x86_64|amd64)
            echo "x64"
            ;;
        aarch64|arm64)
            echo "arm64"
            ;;
        armv7l|armv7)
            echo "armv7l"
            ;;
        *)
            print_message $RED "不支持的架构: $arch"
            exit 1
            ;;
    esac
}

# 获取最新LTS版本
get_latest_lts() {
    print_message $BLUE "正在获取最新LTS版本..."
    
    local lts_version=""
    if command -v curl >/dev/null 2>&1; then
        lts_version=$(curl -s "$NODE_API_URL" | python3 -c "
import sys, json
data = json.load(sys.stdin)
for item in data:
    if item.get('lts') and item['lts'] != False:
        print(item['version'])
        break
" 2>/dev/null)
        
        # 如果Python不可用，使用备用方法
        if [[ -z "$lts_version" ]]; then
            lts_version=$(curl -s "$NODE_API_URL" | grep -A1 '"lts":' | grep -v 'false' | head -1 | grep -o '"version":"[^"]*' | cut -d'"' -f4)
        fi
    elif command -v wget >/dev/null 2>&1; then
        lts_version=$(wget -qO- "$NODE_API_URL" | python3 -c "
import sys, json
data = json.load(sys.stdin)
for item in data:
    if item.get('lts') and item['lts'] != False:
        print(item['version'])
        break
" 2>/dev/null)
        
        if [[ -z "$lts_version" ]]; then
            lts_version=$(wget -qO- "$NODE_API_URL" | grep -A1 '"lts":' | grep -v 'false' | head -1 | grep -o '"version":"[^"]*' | cut -d'"' -f4)
        fi
    else
        print_message $RED "需要curl或wget"
        exit 1
    fi
    
    echo "$lts_version"
}

# 测试下载URL
test_download_url() {
    local version=$1
    local os=$2
    local arch=$3
    
    local filename="node-${version}-${os}-${arch}.tar.xz"
    local download_url="${NODE_DOWNLOAD_BASE}/${version}/${filename}"
    
    print_message $BLUE "测试下载URL: $download_url"
    
    if command -v curl >/dev/null 2>&1; then
        if curl -I -f -s "$download_url" >/dev/null 2>&1; then
            print_message $GREEN "✓ URL可访问"
            return 0
        else
            print_message $RED "✗ URL不可访问"
            return 1
        fi
    elif command -v wget >/dev/null 2>&1; then
        if wget --spider -q "$download_url" 2>/dev/null; then
            print_message $GREEN "✓ URL可访问"
            return 0
        else
            print_message $RED "✗ URL不可访问"
            return 1
        fi
    fi
}

# 主函数
main() {
    print_message $GREEN "=== Node.js 安装调试 ==="
    echo
    
    # 系统信息
    local os=$(get_os)
    local arch=$(get_arch)
    
    print_message $BLUE "系统信息:"
    print_message $GREEN "- 操作系统: $os"
    print_message $GREEN "- 架构: $arch ($(uname -m))"
    echo
    
    # 网络测试
    print_message $BLUE "网络连接测试:"
    if curl -s --connect-timeout 5 https://nodejs.org >/dev/null 2>&1; then
        print_message $GREEN "✓ 可以访问 nodejs.org"
    else
        print_message $RED "✗ 无法访问 nodejs.org"
        exit 1
    fi
    
    # 获取LTS版本
    local lts_version=$(get_latest_lts)
    if [[ -n "$lts_version" ]]; then
        print_message $GREEN "✓ 最新LTS版本: $lts_version"
    else
        print_message $RED "✗ 无法获取LTS版本"
        exit 1
    fi
    
    # 测试下载URL
    if test_download_url "$lts_version" "$os" "$arch"; then
        print_message $GREEN "✓ 下载文件可用"
    else
        print_message $RED "✗ 下载文件不可用"
        
        # 尝试其他版本
        print_message $YELLOW "尝试其他常见版本..."
        local test_versions=("v20.11.0" "v18.19.0" "v16.20.2")
        for test_ver in "${test_versions[@]}"; do
            print_message $BLUE "测试版本: $test_ver"
            if test_download_url "$test_ver" "$os" "$arch"; then
                print_message $GREEN "✓ 版本 $test_ver 可用"
                break
            fi
        done
    fi
    
    echo
    print_message $GREEN "调试完成！"
    print_message $YELLOW "如果所有测试都通过，主安装脚本应该可以正常工作。"
}

main "$@"
