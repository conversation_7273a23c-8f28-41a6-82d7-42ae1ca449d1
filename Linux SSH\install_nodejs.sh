#!/bin/bash

# Node.js 一键安装脚本
# 支持版本选择、目录选择、API拉取最新版本等功能

# 启用严格模式，但允许某些命令失败
set -eE

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认配置
DEFAULT_INSTALL_DIR="/usr/local"
NODE_API_URL="https://nodejs.org/dist/index.json"
NODE_DOWNLOAD_BASE="https://nodejs.org/dist"

# 镜像源配置
MIRROR_SOURCES=(
    "https://nodejs.org/dist"                    # 官方源
    "https://npm.taobao.org/mirrors/node"        # 淘宝镜像
    "https://mirrors.tuna.tsinghua.edu.cn/nodejs-release"  # 清华镜像
    "https://mirrors.ustc.edu.cn/node"           # 中科大镜像
)

# 全局变量
VERSION=""
INSTALL_DIR=""
OS=""
ARCH=""
DISTRO=""

# 错误处理
handle_error() {
    local exit_code=$?
    local line_number=$1
    echo
    print_message $RED "=================================="
    print_message $RED "安装过程中发生错误"
    print_message $RED "=================================="
    print_message $RED "错误位置: 第 $line_number 行"
    print_message $RED "退出码: $exit_code"
    echo
    cleanup_on_error
    print_message $YELLOW "如需帮助，请检查:"
    echo "1. 网络连接是否正常"
    echo "2. 磁盘空间是否充足"
    echo "3. 权限是否正确"
    echo "4. 选择的版本是否支持您的架构"
    exit $exit_code
}

# 错误时清理
cleanup_on_error() {
    print_message $YELLOW "正在清理临时文件..."
    # 清理可能的临时文件
    rm -f /tmp/nodejs_install_*/* 2>/dev/null || true
    rm -rf /tmp/nodejs_install_* 2>/dev/null || true
    rm -f /tmp/nodejs_env_refresh.sh 2>/dev/null || true
    print_message $GREEN "清理完成"
}

# 设置错误处理
trap 'handle_error $LINENO' ERR

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        print_message $BLUE "以root用户运行"
    else
        print_message $BLUE "以普通用户运行，将使用sudo获取必要权限"
    fi
}

# 检测Linux发行版
detect_distro() {
    local distro_id=""
    local distro_name=""

    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        distro_id="$ID"
        distro_name="$NAME"
    elif [[ -f /etc/redhat-release ]]; then
        distro_id="rhel"
        distro_name="Red Hat Enterprise Linux"
    elif [[ -f /etc/debian_version ]]; then
        distro_id="debian"
        distro_name="Debian"
    elif [[ -f /etc/alpine-release ]]; then
        distro_id="alpine"
        distro_name="Alpine Linux"
    elif [[ -f /etc/gentoo-release ]]; then
        distro_id="gentoo"
        distro_name="Gentoo"
    elif [[ -f /etc/slackware-version ]]; then
        distro_id="slackware"
        distro_name="Slackware"
    else
        distro_id="unknown"
        distro_name="Unknown Linux"
    fi

    echo "$distro_id"
}

# 获取发行版详细信息
get_distro_info() {
    local distro_id=$(detect_distro)
    local distro_name="Unknown"
    local distro_family="Unknown"

    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        distro_name="$NAME"

        # 根据ID_LIKE或ID确定家族
        case "$ID" in
            ubuntu|debian|mint|pop|zorin|elementary|kali)
                distro_family="Debian"
                ;;
            rhel|centos|fedora|rocky|almalinux)
                distro_family="Red Hat"
                ;;
            arch|manjaro)
                distro_family="Arch"
                ;;
            opensuse*|sles)
                distro_family="SUSE"
                ;;
            gentoo)
                distro_family="Gentoo"
                ;;
            alpine)
                distro_family="Alpine"
                ;;
            slackware)
                distro_family="Slackware"
                ;;
            *)
                if [[ -n "$ID_LIKE" ]]; then
                    case "$ID_LIKE" in
                        *debian*)
                            distro_family="Debian"
                            ;;
                        *rhel*|*fedora*)
                            distro_family="Red Hat"
                            ;;
                        *arch*)
                            distro_family="Arch"
                            ;;
                        *suse*)
                            distro_family="SUSE"
                            ;;
                    esac
                fi
                ;;
        esac
    fi

    echo "$distro_id|$distro_name|$distro_family"
}

# 安装依赖包
install_dependencies() {
    local distro=$(detect_distro)
    local distro_info=$(get_distro_info)
    local distro_name=$(echo "$distro_info" | cut -d'|' -f2)
    local distro_family=$(echo "$distro_info" | cut -d'|' -f3)

    print_message $BLUE "检测到发行版: $distro_name ($distro_family 家族)"

    # 检查是否需要安装依赖
    local need_install=false
    if ! command -v curl >/dev/null 2>&1 && ! command -v wget >/dev/null 2>&1; then
        need_install=true
    fi
    if ! command -v xz >/dev/null 2>&1 && ! command -v unxz >/dev/null 2>&1; then
        need_install=true
    fi

    if [[ "$need_install" == "false" ]]; then
        print_message $GREEN "所需依赖已安装"
        return 0
    fi

    print_message $BLUE "正在安装依赖包..."

    # 设置sudo命令
    local use_sudo=""
    if [[ $EUID -ne 0 ]]; then
        use_sudo="sudo"
    fi

    case $distro in
        # Debian 家族
        ubuntu|debian|mint|pop|zorin|elementary|kali)
            $use_sudo apt-get update -qq 2>/dev/null || true
            $use_sudo apt-get install -y curl wget xz-utils
            ;;
        # Red Hat 家族
        centos|rhel|fedora|rocky|almalinux)
            if command -v dnf >/dev/null 2>&1; then
                $use_sudo dnf install -y curl wget xz
            else
                $use_sudo yum install -y curl wget xz
            fi
            ;;
        # SUSE 家族
        opensuse*|sles)
            $use_sudo zypper install -y curl wget xz
            ;;
        # Arch 家族
        arch|manjaro)
            $use_sudo pacman -S --noconfirm curl wget xz
            ;;
        # Alpine
        alpine)
            $use_sudo apk add curl wget xz
            ;;
        # Gentoo
        gentoo)
            $use_sudo emerge -q net-misc/curl net-misc/wget app-arch/xz-utils
            ;;
        # Slackware
        slackware)
            print_message $YELLOW "Slackware 用户请手动安装: curl, wget, xz"
            print_message $YELLOW "可以从 SlackBuilds.org 获取相关包"
            read -p "依赖已安装，按回车继续..."
            ;;
        *)
            # 尝试根据家族来安装
            case $distro_family in
                "Debian")
                    $use_sudo apt-get update -qq 2>/dev/null || true
                    $use_sudo apt-get install -y curl wget xz-utils
                    ;;
                "Red Hat")
                    if command -v dnf >/dev/null 2>&1; then
                        $use_sudo dnf install -y curl wget xz
                    else
                        $use_sudo yum install -y curl wget xz
                    fi
                    ;;
                "SUSE")
                    $use_sudo zypper install -y curl wget xz
                    ;;
                "Arch")
                    $use_sudo pacman -S --noconfirm curl wget xz
                    ;;
                *)
                    print_message $YELLOW "未知发行版: $distro_name"
                    print_message $YELLOW "请手动安装以下依赖: curl, wget, xz-utils"
                    read -p "依赖已安装，按回车继续..."
                    ;;
            esac
            ;;
    esac

    # 验证安装
    if command -v curl >/dev/null 2>&1 || command -v wget >/dev/null 2>&1; then
        print_message $GREEN "网络工具安装成功"
    else
        print_message $RED "网络工具安装失败"
        exit 1
    fi

    if command -v xz >/dev/null 2>&1 || command -v unxz >/dev/null 2>&1; then
        print_message $GREEN "解压工具安装成功"
    else
        print_message $RED "解压工具安装失败"
        exit 1
    fi
}

# 检查现有Node.js安装
check_existing_nodejs() {
    if command -v node >/dev/null 2>&1; then
        local current_version=$(node --version 2>/dev/null)
        local current_path=$(which node 2>/dev/null)

        print_message $YELLOW "检测到已安装的Node.js:"
        print_message $YELLOW "- 版本: $current_version"
        print_message $YELLOW "- 路径: $current_path"
        echo

        read -p "是否继续安装新版本? [y/N]: " continue_install
        if [[ ! $continue_install =~ ^[Yy]$ ]]; then
            print_message $BLUE "安装已取消"
            exit 0
        fi

        read -p "是否备份现有安装? [Y/n]: " backup_existing
        if [[ ! $backup_existing =~ ^[Nn]$ ]]; then
            backup_existing_nodejs "$current_path"
        fi
    fi
}

# 备份现有Node.js
backup_existing_nodejs() {
    local node_path=$1
    local backup_dir="$HOME/.nodejs_backup_$(date +%Y%m%d_%H%M%S)"

    print_message $BLUE "正在备份现有Node.js到: $backup_dir"

    mkdir -p "$backup_dir"

    # 备份node相关文件
    if [[ -f "$node_path" ]]; then
        cp "$node_path" "$backup_dir/" 2>/dev/null || true
    fi

    # 备份npm和npx
    local npm_path=$(which npm 2>/dev/null)
    local npx_path=$(which npx 2>/dev/null)

    if [[ -f "$npm_path" ]]; then
        cp "$npm_path" "$backup_dir/" 2>/dev/null || true
    fi

    if [[ -f "$npx_path" ]]; then
        cp "$npx_path" "$backup_dir/" 2>/dev/null || true
    fi

    print_message $GREEN "备份完成: $backup_dir"
}

# 检查系统架构
get_arch() {
    local arch=$(uname -m)
    case $arch in
        x86_64|amd64)
            echo "x64"
            ;;
        aarch64|arm64)
            echo "arm64"
            ;;
        armv7l|armv7)
            echo "armv7l"
            ;;
        armv6l|armv6)
            echo "armv6l"
            ;;
        i386|i686)
            echo "x86"
            ;;
        s390x)
            echo "s390x"
            ;;
        ppc64le|ppc64el)
            echo "ppc64le"
            ;;
        ppc64)
            echo "ppc64"
            ;;
        *)
            print_message $RED "不支持的架构: $arch"
            print_message $YELLOW "Node.js支持的架构: x64, arm64, armv7l, armv6l, x86, s390x, ppc64le, ppc64"
            print_message $YELLOW "您的架构: $arch"
            exit 1
            ;;
    esac
}

# 检查架构兼容性
check_arch_compatibility() {
    local arch=$1
    local version=$2

    # 某些架构在特定版本中可能不可用
    case $arch in
        armv6l)
            # ARMv6 支持在较新版本中可能有限
            if [[ $version =~ ^v([0-9]+) ]]; then
                local major_version=${BASH_REMATCH[1]}
                if [[ $major_version -ge 16 ]]; then
                    print_message $YELLOW "警告: ARMv6 在 Node.js v16+ 中支持有限"
                    print_message $YELLOW "建议使用 Node.js v14 或更早版本"
                fi
            fi
            ;;
        x86)
            # 32位 x86 支持在较新版本中已停止
            if [[ $version =~ ^v([0-9]+) ]]; then
                local major_version=${BASH_REMATCH[1]}
                if [[ $major_version -ge 10 ]]; then
                    print_message $YELLOW "警告: 32位 x86 在 Node.js v10+ 中不再支持"
                    print_message $YELLOW "建议使用 64位系统或 Node.js v8"
                    read -p "是否继续尝试安装? [y/N]: " continue_install
                    if [[ ! $continue_install =~ ^[Yy]$ ]]; then
                        exit 0
                    fi
                fi
            fi
            ;;
        ppc64)
            # Big-endian PowerPC 支持有限
            print_message $YELLOW "注意: PowerPC 64位 (big-endian) 支持有限"
            ;;
        s390x)
            # IBM Z 架构支持
            print_message $BLUE "检测到 IBM Z (s390x) 架构"
            ;;
    esac
}

# 检查操作系统
get_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        echo "linux"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        echo "darwin"
    else
        print_message $RED "不支持的操作系统: $OSTYPE"
        exit 1
    fi
}

# 获取可用的Node.js版本列表
get_available_versions() {
    print_message $BLUE "正在获取Node.js版本列表..."

    if command -v curl >/dev/null 2>&1; then
        curl -s "$NODE_API_URL" | grep -o '"version":"[^"]*' | cut -d'"' -f4
    elif command -v wget >/dev/null 2>&1; then
        wget -qO- "$NODE_API_URL" | grep -o '"version":"[^"]*' | cut -d'"' -f4
    else
        print_message $RED "错误: 需要curl或wget来下载版本信息"
        exit 1
    fi
}

# 分页显示版本列表
show_versions_paginated() {
    local versions=("$@")
    local total=${#versions[@]}
    local per_page=10
    local current_page=1
    local total_pages=$(( (total + per_page - 1) / per_page ))

    while true; do
        clear
        print_message $GREEN "=== Node.js 版本列表 (第 $current_page/$total_pages 页) ==="
        echo

        local start=$(( (current_page - 1) * per_page ))
        local end=$(( start + per_page - 1 ))
        if [[ $end -ge $total ]]; then
            end=$(( total - 1 ))
        fi

        for i in $(seq $start $end); do
            local num=$(( i + 1 ))
            echo "$num) ${versions[$i]}"
        done

        echo
        print_message $YELLOW "导航选项:"
        echo "n) 下一页"
        echo "p) 上一页"
        echo "s) 选择版本 (输入版本号)"
        echo "q) 返回主菜单"
        echo

        read -p "请选择操作: " action

        case $action in
            n|N)
                if [[ $current_page -lt $total_pages ]]; then
                    current_page=$(( current_page + 1 ))
                else
                    print_message $YELLOW "已经是最后一页"
                    sleep 1
                fi
                ;;
            p|P)
                if [[ $current_page -gt 1 ]]; then
                    current_page=$(( current_page - 1 ))
                else
                    print_message $YELLOW "已经是第一页"
                    sleep 1
                fi
                ;;
            s|S)
                read -p "请输入版本号 (如: v18.17.0): " selected_version
                if [[ $selected_version =~ ^v[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                    # 验证版本是否存在
                    for version in "${versions[@]}"; do
                        if [[ "$version" == "$selected_version" ]]; then
                            VERSION="$selected_version"
                            print_message $GREEN "选择的版本: $VERSION"
                            return 0
                        fi
                    done
                    print_message $RED "版本不存在，请重新选择"
                    sleep 2
                else
                    print_message $RED "版本号格式错误，请使用格式: vX.Y.Z"
                    sleep 2
                fi
                ;;
            q|Q)
                return 1
                ;;
            *)
                print_message $RED "无效选择"
                sleep 1
                ;;
        esac
    done
}

# 获取最新LTS版本
get_latest_lts() {
    local lts_version=""

    if command -v curl >/dev/null 2>&1; then
        # 使用更可靠的方法获取LTS版本
        lts_version=$(curl -s "$NODE_API_URL" | grep -E '"version"|"lts"' | grep -B1 '"lts":"[^f]' | grep '"version"' | head -1 | grep -o '"version":"[^"]*' | cut -d'"' -f4)

        # 如果上面的方法失败，尝试备用方法
        if [[ -z "$lts_version" ]]; then
            lts_version=$(curl -s "$NODE_API_URL" | sed -n '/"lts":[^f]/,/}/p' | grep '"version"' | head -1 | grep -o '"version":"[^"]*' | cut -d'"' -f4)
        fi

        # 最后的备用方法：获取第一个非false的LTS版本
        if [[ -z "$lts_version" ]]; then
            lts_version=$(curl -s "$NODE_API_URL" | awk '/"lts":/ && !/false/ {getline; if(/version/) print}' | head -1 | grep -o '"version":"[^"]*' | cut -d'"' -f4)
        fi

    elif command -v wget >/dev/null 2>&1; then
        # 使用wget的相同逻辑
        lts_version=$(wget -qO- "$NODE_API_URL" | grep -E '"version"|"lts"' | grep -B1 '"lts":"[^f]' | grep '"version"' | head -1 | grep -o '"version":"[^"]*' | cut -d'"' -f4)

        if [[ -z "$lts_version" ]]; then
            lts_version=$(wget -qO- "$NODE_API_URL" | sed -n '/"lts":[^f]/,/}/p' | grep '"version"' | head -1 | grep -o '"version":"[^"]*' | cut -d'"' -f4)
        fi

        if [[ -z "$lts_version" ]]; then
            lts_version=$(wget -qO- "$NODE_API_URL" | awk '/"lts":/ && !/false/ {getline; if(/version/) print}' | head -1 | grep -o '"version":"[^"]*' | cut -d'"' -f4)
        fi
    else
        print_message $RED "错误: 需要curl或wget来下载版本信息"
        exit 1
    fi

    echo "$lts_version"
}

# 显示版本选择菜单
show_version_menu() {
    clear
    print_message $GREEN "=== Node.js 一键安装脚本 ==="
    echo
    print_message $BLUE "版本选择选项:"
    echo "1) 最新LTS版本 (推荐)"
    echo "2) 最新稳定版本"
    echo "3) 浏览所有可用版本 (分页显示)"
    echo "4) 手动输入版本号"
    echo "5) 退出"
    echo
}

# 选择安装版本
select_version() {
    while true; do
        show_version_menu
        read -p "请选择安装选项 [1-5]: " choice

        case $choice in
            1)
                print_message $BLUE "正在获取最新LTS版本..."
                VERSION=$(get_latest_lts)
                if [[ -n "$VERSION" ]]; then
                    print_message $GREEN "选择的版本: $VERSION (LTS)"
                    break
                else
                    print_message $RED "无法获取LTS版本信息"
                    read -p "按回车键继续..."
                fi
                ;;
            2)
                print_message $BLUE "正在获取最新稳定版本..."
                if command -v curl >/dev/null 2>&1; then
                    VERSION=$(curl -s "$NODE_API_URL" | grep -o '"version":"[^"]*' | cut -d'"' -f4 | head -1)
                elif command -v wget >/dev/null 2>&1; then
                    VERSION=$(wget -qO- "$NODE_API_URL" | grep -o '"version":"[^"]*' | cut -d'"' -f4 | head -1)
                fi
                if [[ -n "$VERSION" ]]; then
                    print_message $GREEN "选择的版本: $VERSION (最新)"
                    break
                else
                    print_message $RED "无法获取版本信息"
                    read -p "按回车键继续..."
                fi
                ;;
            3)
                print_message $BLUE "正在获取版本列表..."
                local versions_array=()
                while IFS= read -r line; do
                    versions_array+=("$line")
                done < <(get_available_versions)

                if [[ ${#versions_array[@]} -eq 0 ]]; then
                    print_message $RED "无法获取版本列表"
                    read -p "按回车键继续..."
                    continue
                fi

                if show_versions_paginated "${versions_array[@]}"; then
                    break
                fi
                ;;
            4)
                read -p "请输入版本号 (如: v18.17.0): " VERSION
                if [[ $VERSION =~ ^v[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                    print_message $GREEN "选择的版本: $VERSION"
                    break
                else
                    print_message $RED "版本号格式错误，请使用格式: vX.Y.Z"
                    read -p "按回车键继续..."
                fi
                ;;
            5)
                print_message $YELLOW "安装已取消"
                exit 0
                ;;
            *)
                print_message $RED "无效选择，请重新输入"
                read -p "按回车键继续..."
                ;;
        esac
    done
}

# 选择安装目录
select_install_dir() {
    echo
    print_message $GREEN "=== 安装目录选择 ==="
    echo "1) 默认目录: $DEFAULT_INSTALL_DIR"
    echo "2) 用户目录: $HOME/.local"
    echo "3) 自定义目录"
    echo

    read -p "请选择安装目录 [1-3]: " dir_choice
    
    case $dir_choice in
        1)
            INSTALL_DIR="$DEFAULT_INSTALL_DIR"
            ;;
        2)
            INSTALL_DIR="$HOME/.local"
            mkdir -p "$INSTALL_DIR"
            ;;
        3)
            read -p "请输入安装目录路径: " INSTALL_DIR
            if [[ ! -d "$INSTALL_DIR" ]]; then
                read -p "目录不存在，是否创建? [y/N]: " create_dir
                if [[ $create_dir =~ ^[Yy]$ ]]; then
                    if [[ $EUID -eq 0 ]]; then
                        mkdir -p "$INSTALL_DIR" || {
                            print_message $RED "无法创建目录: $INSTALL_DIR"
                            exit 1
                        }
                    else
                        sudo mkdir -p "$INSTALL_DIR" || {
                            print_message $RED "无法创建目录: $INSTALL_DIR"
                            exit 1
                        }
                    fi
                else
                    print_message $RED "安装取消"
                    exit 1
                fi
            fi
            ;;
        *)
            print_message $YELLOW "使用默认目录: $DEFAULT_INSTALL_DIR"
            INSTALL_DIR="$DEFAULT_INSTALL_DIR"
            ;;
    esac
    
    print_message $GREEN "安装目录: $INSTALL_DIR"
}

# 下载Node.js
download_nodejs() {
    local version=$1
    local os=$2
    local arch=$3
    local install_dir=$4

    # 检查架构兼容性
    check_arch_compatibility "$arch" "$version"

    local filename="node-${version}-${os}-${arch}.tar.xz"
    local download_url="${NODE_DOWNLOAD_BASE}/${version}/${filename}"
    local temp_dir="/tmp/nodejs_install_$$"

    print_message $BLUE "正在下载 Node.js ${version} (${os}-${arch})..."
    print_message $BLUE "下载地址: $download_url"

    mkdir -p "$temp_dir"
    cd "$temp_dir"

    # 首先检查文件是否存在
    print_message $BLUE "检查文件可用性..."
    print_message $BLUE "测试URL: $download_url"

    local url_check_failed=false
    if command -v curl >/dev/null 2>&1; then
        local http_code=$(curl -I -s -o /dev/null -w "%{http_code}" "$download_url" 2>/dev/null || echo "000")
        print_message $BLUE "HTTP状态码: $http_code"
        if [[ "$http_code" != "200" ]]; then
            url_check_failed=true
            print_message $YELLOW "URL检查失败，HTTP状态码: $http_code"
        fi
    elif command -v wget >/dev/null 2>&1; then
        if ! wget --spider -q "$download_url" 2>/dev/null; then
            url_check_failed=true
            print_message $YELLOW "wget URL检查失败"
        fi
    fi

    if [[ "$url_check_failed" == "true" ]]; then
        print_message $YELLOW "URL检查失败，但继续尝试下载..."
        print_message $YELLOW "可能的原因:"
        echo "1. 该版本不支持您的架构 ($arch)"
        echo "2. 版本号错误: $version"
        echo "3. 网络连接问题"
        echo "4. 服务器临时不可用"
        echo
        print_message $BLUE "尝试备用下载方法..."
    else
        print_message $GREEN "文件可用，开始下载..."
    fi

    # 下载文件
    print_message $BLUE "开始下载文件..."
    print_message $BLUE "文件: $filename"
    print_message $BLUE "URL: $download_url"

    local download_success=false
    local error_msg=""

    # 确保临时目录存在且可写
    if [[ ! -d "$temp_dir" ]] || [[ ! -w "$temp_dir" ]]; then
        print_message $RED "临时目录问题: $temp_dir"
        return 1
    fi

    # 方法1: 使用curl下载
    if command -v curl >/dev/null 2>&1; then
        print_message $BLUE "使用curl下载..."
        echo "执行命令: curl -L --connect-timeout 30 --max-time 600 --progress-bar -o \"$filename\" \"$download_url\""

        if curl -L --connect-timeout 30 --max-time 600 --progress-bar -o "$filename" "$download_url"; then
            if [[ -f "$filename" ]] && [[ -s "$filename" ]]; then
                download_success=true
                print_message $GREEN "curl下载成功"
            else
                error_msg="curl下载的文件无效或为空"
            fi
        else
            error_msg="curl下载命令失败"
            print_message $YELLOW "curl下载失败，尝试wget..."
        fi
    fi

    # 方法2: 如果curl失败，尝试wget
    if [[ "$download_success" == "false" ]] && command -v wget >/dev/null 2>&1; then
        print_message $BLUE "使用wget下载..."
        echo "执行命令: wget --timeout=30 --tries=3 --progress=bar:force -O \"$filename\" \"$download_url\""

        if wget --timeout=30 --tries=3 --progress=bar:force -O "$filename" "$download_url"; then
            if [[ -f "$filename" ]] && [[ -s "$filename" ]]; then
                download_success=true
                print_message $GREEN "wget下载成功"
            else
                error_msg="wget下载的文件无效或为空"
            fi
        else
            error_msg="wget下载命令失败"
        fi
    fi

    # 如果都失败了，尝试备用方法
    if [[ "$download_success" == "false" ]]; then
        print_message $YELLOW "标准下载失败，尝试备用方法..."

        # 尝试不同的用户代理
        if command -v curl >/dev/null 2>&1; then
            print_message $BLUE "尝试使用不同的用户代理..."
            if curl -L --user-agent "Mozilla/5.0 (Linux; Node.js Installer)" --connect-timeout 30 --max-time 300 -o "$filename" "$download_url" 2>/dev/null; then
                download_success=true
                print_message $GREEN "备用方法下载成功！"
            fi
        fi

        # 如果还是失败，尝试分段下载
        if [[ "$download_success" == "false" ]] && command -v curl >/dev/null 2>&1; then
            print_message $BLUE "尝试分段下载..."
            if curl -L --continue-at - --connect-timeout 30 --max-time 600 -o "$filename" "$download_url" 2>/dev/null; then
                download_success=true
                print_message $GREEN "分段下载成功！"
            fi
        fi
    fi

    # 最终检查
    if [[ "$download_success" == "false" ]]; then
        print_message $RED "所有下载方法都失败了"
        print_message $RED "URL: $download_url"
        print_message $YELLOW "详细故障排除:"
        echo "1. 网络连接测试: curl -I https://nodejs.org"
        echo "2. 手动下载测试: curl -O $download_url"
        echo "3. 检查磁盘空间: df -h /tmp"
        echo "4. 检查DNS: nslookup nodejs.org"
        echo "5. 运行诊断脚本: ./quick_diagnosis.sh"
        echo
        print_message $BLUE "可尝试的替代版本:"
        echo "- v20.16.0: https://nodejs.org/dist/v20.16.0/node-v20.16.0-linux-$arch.tar.xz"
        echo "- v20.11.0: https://nodejs.org/dist/v20.11.0/node-v20.11.0-linux-$arch.tar.xz"
        echo "- v18.19.0: https://nodejs.org/dist/v18.19.0/node-v18.19.0-linux-$arch.tar.xz"

        cleanup_temp "$temp_dir"
        return 1  # 返回错误而不是直接退出
    fi

    # 验证下载的文件
    if [[ ! -f "$filename" ]] || [[ ! -s "$filename" ]]; then
        print_message $RED "下载的文件无效或为空"
        cleanup_temp "$temp_dir"
        exit 1
    fi

    print_message $GREEN "下载完成: $filename ($(du -h "$filename" | cut -f1))"
    echo "$temp_dir/$filename"
}

# 清理临时文件
cleanup_temp() {
    local temp_dir=$1
    if [[ -d "$temp_dir" ]]; then
        rm -rf "$temp_dir"
        print_message $BLUE "已清理临时文件"
    fi
}

# 安装Node.js
install_nodejs() {
    local archive_path=$1
    local install_dir=$2
    local version=$3

    local temp_dir=$(dirname "$archive_path")
    local node_dir="${install_dir}/node-${version}"

    print_message $BLUE "正在解压和安装..."
    print_message $BLUE "解压文件: $(basename "$archive_path")"

    cd "$temp_dir"

    # 解压文件
    if ! tar -xf "$(basename "$archive_path")" 2>&1; then
        print_message $RED "解压失败: $(basename "$archive_path")"
        print_message $YELLOW "可能的原因: 文件损坏、磁盘空间不足、权限问题"
        cleanup_temp "$temp_dir"
        exit 1
    fi

    print_message $GREEN "解压完成"

    # 获取解压后的目录名
    local extracted_dir=$(tar -tf "$(basename "$archive_path")" | head -1 | cut -f1 -d"/")

    # 创建安装目录并移动文件
    if [[ $EUID -eq 0 ]]; then
        mkdir -p "$install_dir"
        mv "$extracted_dir" "$node_dir"
    else
        if [[ "$install_dir" == "$HOME/.local" ]]; then
            mkdir -p "$install_dir"
            mv "$extracted_dir" "$node_dir"
        else
            sudo mkdir -p "$install_dir"
            sudo mv "$extracted_dir" "$node_dir"
            sudo chown -R $(whoami):$(whoami) "$node_dir" 2>/dev/null || true
        fi
    fi

    print_message $GREEN "Node.js 已安装到: $node_dir"

    # 创建符号链接
    create_symlinks "$node_dir" "$install_dir"

    # 清理临时文件
    cleanup_temp "$temp_dir"
}

# 创建符号链接
create_symlinks() {
    local node_dir=$1
    local install_dir=$2
    local bin_dir="${install_dir}/bin"

    print_message $BLUE "正在创建符号链接..."

    # 创建bin目录和符号链接
    if [[ $EUID -eq 0 ]]; then
        mkdir -p "$bin_dir"
        ln -sf "${node_dir}/bin/node" "${bin_dir}/node"
        ln -sf "${node_dir}/bin/npm" "${bin_dir}/npm"
        ln -sf "${node_dir}/bin/npx" "${bin_dir}/npx"
    else
        if [[ "$install_dir" == "$HOME/.local" ]]; then
            mkdir -p "$bin_dir"
            ln -sf "${node_dir}/bin/node" "${bin_dir}/node"
            ln -sf "${node_dir}/bin/npm" "${bin_dir}/npm"
            ln -sf "${node_dir}/bin/npx" "${bin_dir}/npx"
        else
            sudo mkdir -p "$bin_dir"
            sudo ln -sf "${node_dir}/bin/node" "${bin_dir}/node"
            sudo ln -sf "${node_dir}/bin/npm" "${bin_dir}/npm"
            sudo ln -sf "${node_dir}/bin/npx" "${bin_dir}/npx"
        fi
    fi

    print_message $GREEN "符号链接已创建"
}

# 更新PATH环境变量
update_path() {
    local install_dir=$1
    local bin_dir="${install_dir}/bin"

    print_message $BLUE "正在配置环境变量..."

    # 检查PATH中是否已包含bin目录
    if [[ ":$PATH:" != *":$bin_dir:"* ]]; then
        # 添加到各种shell配置文件
        local shell_configs=("$HOME/.bashrc" "$HOME/.zshrc" "$HOME/.profile")
        local export_line="export PATH=\"$bin_dir:\$PATH\""

        for config in "${shell_configs[@]}"; do
            if [[ -f "$config" ]]; then
                if ! grep -q "$bin_dir" "$config"; then
                    echo "" >> "$config"
                    echo "# Node.js PATH - Added by install script" >> "$config"
                    echo "$export_line" >> "$config"
                    print_message $GREEN "已更新 $config"
                fi
            fi
        done

        # 立即生效
        export PATH="$bin_dir:$PATH"

        # 刷新当前shell环境
        refresh_environment "$bin_dir"

        print_message $GREEN "环境变量已配置"
    else
        print_message $YELLOW "PATH中已包含Node.js路径"
    fi
}

# 刷新环境变量
refresh_environment() {
    local bin_dir=$1

    print_message $BLUE "正在刷新环境变量..."

    # 更新当前shell的PATH
    export PATH="$bin_dir:$PATH"

    # 创建临时脚本来更新环境
    local temp_script="/tmp/nodejs_env_refresh.sh"
    cat > "$temp_script" << EOF
#!/bin/bash
export PATH="$bin_dir:\$PATH"
export NODE_PATH="$bin_dir/../lib/node_modules"
EOF

    # 使脚本可执行
    chmod +x "$temp_script"

    # 尝试刷新不同shell的环境
    if [[ -n "$BASH_VERSION" ]]; then
        source "$temp_script" 2>/dev/null || true
    fi

    if [[ -n "$ZSH_VERSION" ]]; then
        source "$temp_script" 2>/dev/null || true
    fi

    # 清理临时文件
    rm -f "$temp_script"

    # 验证PATH更新
    if command -v node >/dev/null 2>&1; then
        print_message $GREEN "环境变量刷新成功"
    else
        print_message $YELLOW "环境变量已配置，请重新加载shell或重启终端"
    fi
}

# 验证安装
verify_installation() {
    print_message $BLUE "正在验证安装..."

    if command -v node >/dev/null 2>&1; then
        local node_version=$(node --version)
        print_message $GREEN "Node.js 版本: $node_version"
    else
        print_message $RED "Node.js 未正确安装"
        return 1
    fi

    if command -v npm >/dev/null 2>&1; then
        local npm_version=$(npm --version)
        print_message $GREEN "npm 版本: $npm_version"
    else
        print_message $RED "npm 未正确安装"
        return 1
    fi

    return 0
}

# 显示安装完成信息
show_completion_message() {
    local distro_info=$(get_distro_info)
    local distro_id=$(echo "$distro_info" | cut -d'|' -f1)
    local distro_name=$(echo "$distro_info" | cut -d'|' -f2)
    local distro_family=$(echo "$distro_info" | cut -d'|' -f3)

    echo
    print_message $GREEN "=================================="
    print_message $GREEN "   Node.js 安装完成！"
    print_message $GREEN "=================================="
    echo
    print_message $BLUE "系统信息:"
    print_message $BLUE "- 发行版: $distro_name"
    print_message $BLUE "- 家族: $distro_family"
    print_message $BLUE "- 操作系统: $OS"
    print_message $BLUE "- 架构: $ARCH ($(uname -m))"
    echo
    print_message $BLUE "安装信息:"
    print_message $BLUE "- Node.js 版本: $VERSION"
    print_message $BLUE "- 安装目录: $INSTALL_DIR"
    print_message $BLUE "- 二进制文件: $INSTALL_DIR/bin/"
    echo

    # 显示当前版本信息
    if command -v node >/dev/null 2>&1; then
        local node_ver=$(node --version 2>/dev/null)
        local npm_ver=$(npm --version 2>/dev/null)
        print_message $GREEN "✓ Node.js: $node_ver"
        print_message $GREEN "✓ npm: $npm_ver"
    fi

    echo
    print_message $YELLOW "使用说明:"
    echo "1. 环境变量已自动配置并刷新"
    echo "2. 如果命令不可用，请运行以下命令之一:"
    echo "   - source ~/.bashrc"
    echo "   - source ~/.zshrc"
    echo "   - 或重启终端"
    echo "3. 验证安装: node --version && npm --version"
    echo
    print_message $BLUE "常用命令:"
    echo "- 查看版本: node --version"
    echo "- 安装包: npm install <package>"
    echo "- 全局安装: npm install -g <package>"
    echo "- 创建项目: npm init"
    echo
    print_message $GREEN "开始使用Node.js吧！"
    echo
}

# 主函数
main() {
    # 显示欢迎信息
    clear
    print_message $GREEN "=================================="
    print_message $GREEN "   Node.js 一键安装脚本"
    print_message $GREEN "=================================="
    read -p "按回车键开始安装..."

    # 检查系统
    check_root

    # 检查现有安装
    check_existing_nodejs

    # 安装依赖
    install_dependencies

    # 获取系统信息
    OS=$(get_os)
    ARCH=$(get_arch)
    local distro_info=$(get_distro_info)
    local distro_name=$(echo "$distro_info" | cut -d'|' -f2)
    local distro_family=$(echo "$distro_info" | cut -d'|' -f3)

    print_message $BLUE "系统信息: $distro_name ($distro_family, $OS-$ARCH)"

    # 选择版本
    select_version

    # 选择安装目录
    select_install_dir

    # 确认安装
    echo
    print_message $YELLOW "安装确认:"
    print_message $YELLOW "版本: $VERSION"
    print_message $YELLOW "系统: $OS-$ARCH"
    print_message $YELLOW "安装目录: $INSTALL_DIR"
    echo
    read -p "确认安装? [Y/n]: " confirm

    if [[ $confirm =~ ^[Nn]$ ]]; then
        print_message $YELLOW "安装已取消"
        exit 0
    fi

    # 开始安装
    echo
    print_message $GREEN "=================================="
    print_message $GREEN "开始安装 Node.js $VERSION"
    print_message $GREEN "=================================="
    echo

    # 下载
    print_message $BLUE "步骤 1/4: 下载 Node.js"
    ARCHIVE_PATH=$(download_nodejs "$VERSION" "$OS" "$ARCH" "$INSTALL_DIR")

    if [[ -z "$ARCHIVE_PATH" ]] || [[ ! -f "$ARCHIVE_PATH" ]]; then
        print_message $RED "下载失败，安装终止"
        exit 1
    fi

    # 安装
    print_message $BLUE "步骤 2/4: 安装 Node.js"
    install_nodejs "$ARCHIVE_PATH" "$INSTALL_DIR" "$VERSION"

    # 配置环境变量
    print_message $BLUE "步骤 3/4: 配置环境变量"
    update_path "$INSTALL_DIR"

    # 验证安装
    print_message $BLUE "步骤 4/4: 验证安装"
    if verify_installation; then
        show_completion_message
    else
        print_message $RED "安装验证失败，请检查配置"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "Node.js 一键安装脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -v, --version  显示脚本版本"
    echo "  -t, --test     运行发行版检测测试"
    echo
    echo "功能特性:"
    echo "  ✓ 支持多种Linux发行版"
    echo "  ✓ 自动版本选择和下载"
    echo "  ✓ 分页浏览历史版本"
    echo "  ✓ 自动配置环境变量"
    echo "  ✓ 智能依赖管理"
    echo
    echo "示例:"
    echo "  $0              # 交互式安装"
    echo "  $0 --test       # 测试系统兼容性"
    echo "  $0 --help       # 显示帮助"
    echo
}

# 显示版本信息
show_version() {
    echo "Node.js 一键安装脚本 v1.4.0"
    echo "支持的架构: x64, ARM64, ARMv7"
    echo "支持的系统: Linux (多发行版)"
    echo
}

# 运行系统测试
run_test() {
    print_message $GREEN "=== 系统兼容性测试 ==="
    echo

    # 获取系统信息
    local os=$(get_os)
    local arch=$(get_arch)
    local distro_info=$(get_distro_info)
    local distro_name=$(echo "$distro_info" | cut -d'|' -f2)
    local distro_family=$(echo "$distro_info" | cut -d'|' -f3)

    print_message $BLUE "系统信息:"
    print_message $GREEN "- 操作系统: $os"
    print_message $GREEN "- 架构: $arch"
    print_message $GREEN "- 发行版: $distro_name"
    print_message $GREEN "- 家族: $distro_family"
    echo

    # 检查网络连接
    print_message $BLUE "网络连接测试:"
    if curl -s --connect-timeout 5 https://nodejs.org >/dev/null 2>&1; then
        print_message $GREEN "✓ 可以访问 nodejs.org"
    else
        print_message $RED "✗ 无法访问 nodejs.org"
    fi

    # 检查工具
    print_message $BLUE "工具检查:"
    local tools=("curl" "wget" "xz" "tar" "sudo")
    for tool in "${tools[@]}"; do
        if command -v "$tool" >/dev/null 2>&1; then
            print_message $GREEN "✓ $tool 可用"
        else
            print_message $YELLOW "✗ $tool 不可用"
        fi
    done

    echo
    print_message $GREEN "测试完成！脚本应该可以在此系统上正常运行。"
}

# 脚本入口点
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    case "${1:-}" in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--version)
            show_version
            exit 0
            ;;
        -t|--test)
            run_test
            exit 0
            ;;
        "")
            main "$@"
            ;;
        *)
            print_message $RED "未知选项: $1"
            echo "使用 $0 --help 查看帮助信息"
            exit 1
            ;;
    esac
fi
