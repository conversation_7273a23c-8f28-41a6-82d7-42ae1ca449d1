#!/bin/bash

# 快速诊断Node.js下载问题

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_message $GREEN "=== Node.js 下载问题快速诊断 ==="
echo

# 1. 系统信息
print_message $BLUE "1. 系统信息:"
echo "操作系统: $(uname -s)"
echo "架构: $(uname -m)"
echo "内核: $(uname -r)"
echo

# 2. 网络连接测试
print_message $BLUE "2. 网络连接测试:"
if ping -c 1 nodejs.org >/dev/null 2>&1; then
    print_message $GREEN "✓ 可以ping通 nodejs.org"
else
    print_message $RED "✗ 无法ping通 nodejs.org"
fi

if curl -s --connect-timeout 5 https://nodejs.org >/dev/null 2>&1; then
    print_message $GREEN "✓ 可以访问 https://nodejs.org"
else
    print_message $RED "✗ 无法访问 https://nodejs.org"
fi
echo

# 3. 工具检查
print_message $BLUE "3. 下载工具检查:"
if command -v curl >/dev/null 2>&1; then
    curl_version=$(curl --version | head -1)
    print_message $GREEN "✓ curl 可用: $curl_version"
else
    print_message $RED "✗ curl 不可用"
fi

if command -v wget >/dev/null 2>&1; then
    wget_version=$(wget --version | head -1)
    print_message $GREEN "✓ wget 可用: $wget_version"
else
    print_message $RED "✗ wget 不可用"
fi
echo

# 4. 磁盘空间检查
print_message $BLUE "4. 磁盘空间检查:"
echo "/tmp 目录:"
df -h /tmp | tail -1
echo "/Files 目录:"
df -h /Files 2>/dev/null || echo "目录不存在或无权限访问"
echo

# 5. 权限检查
print_message $BLUE "5. 权限检查:"
echo "当前用户: $(whoami)"
echo "用户组: $(groups)"
if [[ -w /tmp ]]; then
    print_message $GREEN "✓ /tmp 目录可写"
else
    print_message $RED "✗ /tmp 目录不可写"
fi

if [[ -d /Files ]]; then
    if [[ -w /Files ]]; then
        print_message $GREEN "✓ /Files 目录可写"
    else
        print_message $RED "✗ /Files 目录不可写"
    fi
else
    print_message $YELLOW "! /Files 目录不存在"
fi
echo

# 6. 测试具体的Node.js下载
print_message $BLUE "6. 测试Node.js下载URL:"

# 获取系统架构
case $(uname -m) in
    x86_64) arch="x64" ;;
    aarch64|arm64) arch="arm64" ;;
    armv7l) arch="armv7l" ;;
    *) arch="unknown" ;;
esac

# 测试几个常见版本
test_versions=("v22.11.0" "v20.11.0" "v18.19.0")
for version in "${test_versions[@]}"; do
    url="https://nodejs.org/dist/$version/node-$version-linux-$arch.tar.xz"
    print_message $BLUE "测试: $version ($arch)"
    echo "URL: $url"
    
    if command -v curl >/dev/null 2>&1; then
        http_code=$(curl -I -s -o /dev/null -w "%{http_code}" "$url" 2>/dev/null || echo "000")
        if [[ "$http_code" == "200" ]]; then
            print_message $GREEN "✓ 可用 (HTTP $http_code)"
        else
            print_message $RED "✗ 不可用 (HTTP $http_code)"
        fi
    elif command -v wget >/dev/null 2>&1; then
        if wget --spider -q "$url" 2>/dev/null; then
            print_message $GREEN "✓ 可用"
        else
            print_message $RED "✗ 不可用"
        fi
    fi
    echo
done

# 7. DNS解析测试
print_message $BLUE "7. DNS解析测试:"
if nslookup nodejs.org >/dev/null 2>&1; then
    print_message $GREEN "✓ DNS解析正常"
    echo "nodejs.org 解析为:"
    nslookup nodejs.org | grep "Address:" | tail -1
else
    print_message $RED "✗ DNS解析失败"
fi
echo

# 8. 代理检查
print_message $BLUE "8. 代理设置检查:"
if [[ -n "$http_proxy" ]]; then
    print_message $YELLOW "HTTP代理: $http_proxy"
fi
if [[ -n "$https_proxy" ]]; then
    print_message $YELLOW "HTTPS代理: $https_proxy"
fi
if [[ -n "$HTTP_PROXY" ]]; then
    print_message $YELLOW "HTTP_PROXY: $HTTP_PROXY"
fi
if [[ -n "$HTTPS_PROXY" ]]; then
    print_message $YELLOW "HTTPS_PROXY: $HTTPS_PROXY"
fi
if [[ -z "$http_proxy" && -z "$https_proxy" && -z "$HTTP_PROXY" && -z "$HTTPS_PROXY" ]]; then
    print_message $GREEN "✓ 未设置代理"
fi
echo

# 9. 建议
print_message $BLUE "9. 建议解决方案:"
echo "基于以上检查结果，建议："
echo
echo "如果网络连接正常但下载失败："
echo "1. 尝试手动下载测试："
echo "   curl -O https://nodejs.org/dist/v20.11.0/node-v20.11.0-linux-$arch.tar.xz"
echo
echo "2. 检查防火墙设置"
echo
echo "3. 如果在企业网络环境，可能需要配置代理"
echo
echo "4. 尝试使用不同的DNS服务器："
echo "   echo 'nameserver 8.8.8.8' | sudo tee /etc/resolv.conf"
echo
echo "5. 如果磁盘空间不足，清理临时文件："
echo "   sudo rm -rf /tmp/nodejs_install_*"
echo

print_message $GREEN "诊断完成！"
print_message $YELLOW "请将此输出发送给技术支持以获得进一步帮助。"
