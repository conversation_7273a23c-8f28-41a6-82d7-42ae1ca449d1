# Node.js 安装脚本故障排除指南

## 问题诊断

根据您提供的输出，脚本在"开始安装 Node.js"后停止了。以下是可能的原因和解决方案：

## 1. 运行调试脚本

首先运行调试脚本来检查系统兼容性：

```bash
chmod +x debug_nodejs.sh
./debug_nodejs.sh
```

这将测试：
- 网络连接
- 版本获取
- 下载URL可用性

## 2. 常见问题和解决方案

### 问题1: 版本显示重复
**现象**: 显示 "正在获取最新LTS版本...v22.17.1 (LTS)"

**原因**: LTS版本获取函数中有多余的输出

**解决方案**: 已在最新版本中修复

### 问题2: 脚本在下载后停止
**可能原因**:
1. 网络连接中断
2. 下载的文件损坏
3. 磁盘空间不足
4. 权限问题

**调试步骤**:
```bash
# 检查磁盘空间
df -h /tmp
df -h /Files

# 检查权限
ls -la /Files/
mkdir -p /Files/Node && echo "权限OK" || echo "权限问题"

# 手动测试下载
curl -I https://nodejs.org/dist/v22.17.1/node-v22.17.1-linux-x64.tar.xz
```

### 问题3: 架构不支持
**检查命令**:
```bash
uname -m  # 查看系统架构
```

**支持的架构**:
- x86_64 → x64
- aarch64/arm64 → arm64  
- armv7l → armv7l
- armv6l → armv6l
- i386/i686 → x86
- s390x → s390x
- ppc64le → ppc64le

## 3. 手动安装步骤

如果自动脚本失败，可以手动安装：

```bash
# 1. 设置变量
VERSION="v22.17.1"  # 或您想要的版本
ARCH="x64"          # 根据 uname -m 确定
INSTALL_DIR="/Files/Node"

# 2. 下载
cd /tmp
wget https://nodejs.org/dist/$VERSION/node-$VERSION-linux-$ARCH.tar.xz

# 3. 解压
tar -xf node-$VERSION-linux-$ARCH.tar.xz

# 4. 安装
mkdir -p $INSTALL_DIR
mv node-$VERSION-linux-$ARCH $INSTALL_DIR/node-$VERSION

# 5. 创建符号链接
mkdir -p $INSTALL_DIR/bin
ln -sf $INSTALL_DIR/node-$VERSION/bin/node $INSTALL_DIR/bin/node
ln -sf $INSTALL_DIR/node-$VERSION/bin/npm $INSTALL_DIR/bin/npm
ln -sf $INSTALL_DIR/node-$VERSION/bin/npx $INSTALL_DIR/bin/npx

# 6. 配置环境变量
echo 'export PATH="/Files/Node/bin:$PATH"' >> ~/.bashrc
source ~/.bashrc

# 7. 验证
node --version
npm --version
```

## 4. 脚本改进建议

### 启用详细输出
在脚本开头添加：
```bash
set -x  # 显示执行的每个命令
```

### 检查特定错误
```bash
# 在脚本中添加更多检查点
echo "DEBUG: 开始下载..."
echo "DEBUG: 下载完成，文件大小: $(ls -lh $filename)"
echo "DEBUG: 开始解压..."
```

## 5. 环境要求检查

确保系统满足以下要求：

```bash
# 检查必需工具
command -v curl || command -v wget || echo "需要安装 curl 或 wget"
command -v tar || echo "需要安装 tar"
command -v xz || echo "需要安装 xz-utils"

# 检查网络
ping -c 1 nodejs.org || echo "网络连接问题"

# 检查磁盘空间 (至少需要 100MB)
df -h /tmp | awk 'NR==2 {print $4}'
```

## 6. 日志收集

如果问题持续存在，请收集以下信息：

```bash
# 系统信息
uname -a
cat /etc/os-release
df -h
free -h

# 网络测试
curl -I https://nodejs.org/dist/index.json
curl -I https://nodejs.org/dist/v22.17.1/node-v22.17.1-linux-x64.tar.xz

# 权限测试
id
groups
sudo -l
```

## 7. 联系支持

如果以上步骤都无法解决问题，请提供：
1. 调试脚本的完整输出
2. 系统信息 (`uname -a`)
3. 错误发生时的完整日志
4. 网络环境信息（是否使用代理等）

## 8. 快速修复

对于您当前的情况，建议：

1. **立即尝试**: 运行调试脚本确认问题
2. **如果网络正常**: 使用手动安装步骤
3. **如果是权限问题**: 确保对 `/Files` 目录有写权限
4. **如果是架构问题**: 检查 Node.js 是否支持您的架构

```bash
# 快速检查
./debug_nodejs.sh
# 如果调试通过，重新运行主脚本
./install_nodejs.sh
```
